import React, { createContext, useContext, useState } from 'react'

const StoreContext = createContext()

export const useStore = () => {
  const context = useContext(StoreContext)
  if (!context) {
    throw new Error('useStore must be used within a StoreProvider')
  }
  return context
}

// Mock stores data
const mockStores = [
  {
    id: '1',
    name: 'Main Store',
    address: '123 Main St, City, State',
    phone: '+****************',
    isActive: true
  },
  {
    id: '2',
    name: 'Downtown Branch',
    address: '456 Downtown Ave, City, State',
    phone: '+****************',
    isActive: true
  },
  {
    id: '3',
    name: 'Mall Location',
    address: '789 Mall Blvd, City, State',
    phone: '+****************',
    isActive: true
  }
]

export const StoreProvider = ({ children }) => {
  const [stores] = useState(mockStores)
  const [currentStore, setCurrentStore] = useState(mockStores[0])

  const switchStore = (storeId) => {
    const store = stores.find(s => s.id === storeId)
    if (store) {
      setCurrentStore(store)
      localStorage.setItem('currentStoreId', storeId)
    }
  }

  return (
    <StoreContext.Provider value={{ 
      stores, 
      currentStore, 
      switchStore 
    }}>
      {children}
    </StoreContext.Provider>
  )
}
