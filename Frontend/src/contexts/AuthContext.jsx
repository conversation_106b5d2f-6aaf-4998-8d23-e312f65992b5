import React, { createContext, useContext, useState } from "react";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Mock users with SaaS structure
const mockUsers = [
  // Super Admin - Manages the entire SaaS platform
  {
    id: "1",
    username: "superadmin",
    password: "superadmin123",
    name: "Super Admin",
    email: "<EMAIL>",
    role: "super_admin",
    userType: "platform_admin",
    organizationId: null,
    permissions: ["platform_all"],
  },

  // Organization Owner - Customer who owns their POS system
  {
    id: "2",
    username: "owner1",
    password: "owner123",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "owner",
    userType: "tenant",
    organizationId: "org_1",
    organizationName: "Smith Restaurant Group",
    permissions: ["org_all"],
  },

  // Manager - Can manage most things in their organization
  {
    id: "3",
    username: "manager1",
    password: "manager123",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "manager",
    userType: "team_member",
    organizationId: "org_1",
    organizationName: "Smith Restaurant Group",
    permissions: [
      "pos",
      "inventory",
      "reports",
      "customers",
      "orders_read",
      "orders_update",
    ],
  },

  // Cashier - Limited access to POS only
  {
    id: "4",
    username: "cashier1",
    password: "cashier123",
    name: "Mike Davis",
    email: "<EMAIL>",
    role: "cashier",
    userType: "team_member",
    organizationId: "org_1",
    organizationName: "Smith Restaurant Group",
    permissions: ["pos"],
  },

  // Another organization owner
  {
    id: "5",
    username: "owner2",
    password: "owner123",
    name: "Lisa Chen",
    email: "<EMAIL>",
    role: "owner",
    userType: "tenant",
    organizationId: "org_2",
    organizationName: "Chen Retail Store",
    permissions: ["org_all"],
  },
];

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    const savedUser = localStorage.getItem("user");
    return savedUser ? JSON.parse(savedUser) : null;
  });

  const login = (username, password) => {
    const foundUser = mockUsers.find(
      (u) => u.username === username && u.password === password
    );

    if (foundUser) {
      const { password: _, ...userWithoutPassword } = foundUser;
      setUser(userWithoutPassword);
      localStorage.setItem("user", JSON.stringify(userWithoutPassword));
      return { success: true };
    }

    return { success: false, error: "Invalid credentials" };
  };

  const register = (formData) => {
    // Check if username already exists
    const existingUser = mockUsers.find(
      (u) => u.username === formData.username
    );
    if (existingUser) {
      return { success: false, error: "Username already exists" };
    }

    // Check if email already exists
    const existingEmail = mockUsers.find((u) => u.email === formData.email);
    if (existingEmail) {
      return { success: false, error: "Email already exists" };
    }

    // Generate new user ID
    const newId = (mockUsers.length + 1).toString();
    const newOrgId = `org_${newId}`;

    // Create new user (organization owner by default for registration)
    const newUser = {
      id: newId,
      username: formData.username,
      password: formData.password,
      name: formData.name,
      email: formData.email,
      role: "owner",
      userType: "tenant",
      organizationId: newOrgId,
      organizationName: formData.organizationName,
      permissions: ["org_all"],
    };

    // Add to mock users array (in real app, this would be an API call)
    mockUsers.push(newUser);

    // Auto-login the new user
    const { password: _, ...userWithoutPassword } = newUser;
    setUser(userWithoutPassword);
    localStorage.setItem("user", JSON.stringify(userWithoutPassword));

    return { success: true };
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem("user");
  };

  const hasPermission = (permission) => {
    if (!user) return false;

    // Super admin has access to everything
    if (user.permissions.includes("platform_all")) return true;

    // Organization owner has access to all org features
    if (user.permissions.includes("org_all")) {
      // But not platform admin features
      if (permission === "platform_all") return false;
      return true;
    }

    // Check specific permissions
    return user.permissions.includes(permission);
  };

  const isSuperAdmin = () => {
    return user?.userType === "platform_admin";
  };

  const isOrganizationOwner = () => {
    return user?.userType === "tenant";
  };

  const isTeamMember = () => {
    return user?.userType === "team_member";
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        register,
        logout,
        hasPermission,
        isSuperAdmin,
        isOrganizationOwner,
        isTeamMember,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
