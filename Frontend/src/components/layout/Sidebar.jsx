import React from "react";
import { NavLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { cn } from "../../lib/utils";
import {
  X,
  LayoutDashboard,
  ShoppingCart,
  Package,
  Users,
  BarChart3,
  Settings,
  Store,
  CreditCard,
  Building2,
} from "lucide-react";
import { Button } from "../ui/button";

const Sidebar = ({ isOpen, onClose }) => {
  const { hasPermission, isSuperAdmin, isOrganizationOwner, isTeamMember } =
    useAuth();

  // Super Admin Navigation
  const superAdminNavigation = [
    {
      name: "Platform Overview",
      href: "/super-admin",
      icon: LayoutDashboard,
      permission: "platform_all",
    },
    {
      name: "Organizations",
      href: "/super-admin/organizations",
      icon: Building2,
      permission: "platform_all",
    },
    {
      name: "Platform Users",
      href: "/super-admin/platform-users",
      icon: Users,
      permission: "platform_all",
    },
    {
      name: "Analytics",
      href: "/super-admin/analytics",
      icon: BarChart3,
      permission: "platform_all",
    },
    {
      name: "Platform Settings",
      href: "/super-admin/settings",
      icon: Settings,
      permission: "platform_all",
    },
  ];

  // Organization/Tenant Navigation
  const organizationNavigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      permission: "org_all",
    },
    {
      name: "POS",
      href: "/pos",
      icon: CreditCard,
      permission: "pos",
    },
    {
      name: "Products",
      href: "/products",
      icon: Package,
      permission: "inventory",
    },
    {
      name: "Orders",
      href: "/orders",
      icon: ShoppingCart,
      permission: "orders_read",
    },
    {
      name: "Customers",
      href: "/customers",
      icon: Users,
      permission: "customers",
    },
    {
      name: "Reports",
      href: "/reports",
      icon: BarChart3,
      permission: "reports",
    },
    {
      name: "Team",
      href: "/team",
      icon: Users,
      permission: "org_all",
    },
    {
      name: "Stores",
      href: "/stores",
      icon: Store,
      permission: "org_all",
    },
    {
      name: "Settings",
      href: "/settings",
      icon: Settings,
      permission: "org_all",
    },
  ];

  // Get navigation based on user type
  const getNavigation = () => {
    if (isSuperAdmin()) {
      return superAdminNavigation;
    }
    return organizationNavigation;
  };

  const navigation = getNavigation();
  const filteredNavigation = navigation.filter((item) =>
    hasPermission(item.permission)
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <Store className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Ultra POS
              </h1>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="lg:hidden"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {filteredNavigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={() => onClose()}
                className={({ isActive }) =>
                  cn(
                    "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                    isActive
                      ? "bg-blue-600 text-white dark:bg-blue-500"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700"
                  )
                }
              >
                <item.icon className="h-5 w-5" />
                <span>{item.name}</span>
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
