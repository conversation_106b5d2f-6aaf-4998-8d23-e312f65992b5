import React from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useStore } from "../../contexts/StoreContext";
import { useTheme } from "../../contexts/ThemeContext";
import { Button } from "../ui/button";
import {
  Menu,
  Moon,
  Sun,
  Store,
  ChevronDown,
  LogOut,
  User,
} from "lucide-react";

const Header = ({ onMenuClick }) => {
  const { user, logout, isSuperAdmin } = useAuth();
  const { currentStore, stores, switchStore } = useStore();
  const { theme, toggleTheme } = useTheme();

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Menu button and store selector */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Store selector or Organization name */}
          <div className="flex items-center space-x-2">
            <Store className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            {isSuperAdmin() ? (
              <span className="text-sm font-medium">Ultra POS Platform</span>
            ) : user?.organizationName ? (
              <div className="flex flex-col">
                <span className="text-sm font-medium">
                  {user.organizationName}
                </span>
                <select
                  value={currentStore.id}
                  onChange={(e) => switchStore(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-white"
                >
                  {stores.map((store) => (
                    <option key={store.id} value={store.id}>
                      {store.name}
                    </option>
                  ))}
                </select>
              </div>
            ) : (
              <select
                value={currentStore.id}
                onChange={(e) => switchStore(e.target.value)}
                className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-white"
              >
                {stores.map((store) => (
                  <option key={store.id} value={store.id}>
                    {store.name}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        {/* Right side - Theme toggle, user info, logout */}
        <div className="flex items-center space-x-4">
          {/* Theme toggle */}
          <Button variant="ghost" size="icon" onClick={toggleTheme}>
            {theme === "light" ? (
              <Moon className="h-4 w-4" />
            ) : (
              <Sun className="h-4 w-4" />
            )}
          </Button>

          {/* User info */}
          <div className="flex items-center space-x-2 text-sm">
            <User className="h-4 w-4" />
            <span className="font-medium">{user?.name}</span>
            <span className="text-gray-500 dark:text-gray-400">
              ({user?.role})
            </span>
          </div>

          {/* Logout button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={logout}
            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
