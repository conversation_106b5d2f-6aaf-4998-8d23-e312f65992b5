import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ThemeProvider } from "./contexts/ThemeContext";
import { AuthProvider } from "./contexts/AuthContext";
import { StoreProvider } from "./contexts/StoreContext";
import LoginPage from "./pages/LoginPage";
import RegisterPage from "./pages/RegisterPage";
import DashboardLayout from "./components/layout/DashboardLayout";
import AdminDashboard from "./pages/admin/AdminDashboard";
import SuperAdminDashboard from "./pages/super-admin/SuperAdminDashboard";
import POSScreen from "./pages/pos/POSScreen";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import DashboardRedirect from "./components/DashboardRedirect";
import TeamManagementPage from "./pages/team/TeamManagementPage";

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <StoreProvider>
          <Router>
            <div className="min-h-screen bg-white dark:bg-gray-900">
              <Routes>
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <DashboardLayout />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<DashboardRedirect />} />

                  {/* Super Admin Routes */}
                  <Route
                    path="super-admin/*"
                    element={
                      <ProtectedRoute requiredPermission="platform_all">
                        <SuperAdminDashboard />
                      </ProtectedRoute>
                    }
                  />

                  {/* Organization/Tenant Routes */}
                  <Route
                    path="dashboard/*"
                    element={
                      <ProtectedRoute requiredPermission="org_all">
                        <AdminDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="pos"
                    element={
                      <ProtectedRoute requiredPermission="pos">
                        <POSScreen />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products"
                    element={
                      <ProtectedRoute requiredPermission="inventory">
                        <div>Products Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="orders"
                    element={
                      <ProtectedRoute requiredPermission="orders_read">
                        <div>Orders Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="customers"
                    element={
                      <ProtectedRoute requiredPermission="customers">
                        <div>Customers Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="reports"
                    element={
                      <ProtectedRoute requiredPermission="reports">
                        <div>Reports Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="team"
                    element={
                      <ProtectedRoute requiredPermission="org_all">
                        <TeamManagementPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="stores"
                    element={
                      <ProtectedRoute requiredPermission="org_all">
                        <div>Stores Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="settings"
                    element={
                      <ProtectedRoute requiredPermission="org_all">
                        <div>Settings Page Coming Soon</div>
                      </ProtectedRoute>
                    }
                  />
                </Route>
              </Routes>
            </div>
          </Router>
        </StoreProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
