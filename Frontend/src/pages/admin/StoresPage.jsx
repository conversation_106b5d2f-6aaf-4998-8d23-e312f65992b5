import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Store } from 'lucide-react'

const StoresPage = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Stores</h1>
        <p className="text-muted-foreground">
          Manage store locations and settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Store Management
          </CardTitle>
          <CardDescription>
            This page will contain store management functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Store className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Store Management Coming Soon</h3>
            <p className="text-muted-foreground">
              This section will include store configuration, locations, and multi-store settings.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default StoresPage
