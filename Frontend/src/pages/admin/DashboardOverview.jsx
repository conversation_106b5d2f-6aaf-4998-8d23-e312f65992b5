import React from "react";
import { useStore } from "../../contexts/StoreContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  TrendingDown,
} from "lucide-react";

const DashboardOverview = () => {
  const { currentStore } = useStore();

  // Mock data for dashboard
  const stats = [
    {
      title: "Today's Sales",
      value: "$2,847.50",
      change: "+12.5%",
      trend: "up",
      icon: DollarSign,
      description: "Compared to yesterday",
    },
    {
      title: "Orders",
      value: "47",
      change: "+8.2%",
      trend: "up",
      icon: ShoppingCart,
      description: "Orders processed today",
    },
    {
      title: "Customers",
      value: "1,247",
      change: "+3.1%",
      trend: "up",
      icon: Users,
      description: "Total active customers",
    },
    {
      title: "Products",
      value: "892",
      change: "-2.4%",
      trend: "down",
      icon: Package,
      description: "Products in inventory",
    },
  ];

  const recentOrders = [
    {
      id: "#001",
      customer: "John Doe",
      amount: "$45.99",
      status: "Completed",
      time: "2 min ago",
    },
    {
      id: "#002",
      customer: "Jane <PERSON>",
      amount: "$78.50",
      status: "Completed",
      time: "5 min ago",
    },
    {
      id: "#003",
      customer: "Bob Johnson",
      amount: "$123.75",
      status: "Processing",
      time: "8 min ago",
    },
    {
      id: "#004",
      customer: "Alice Brown",
      amount: "$67.25",
      status: "Completed",
      time: "12 min ago",
    },
    {
      id: "#005",
      customer: "Charlie Wilson",
      amount: "$89.99",
      status: "Completed",
      time: "15 min ago",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Welcome back! Here's what's happening at {currentStore.name} today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                {stat.trend === "up" ? (
                  <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                )}
                <span
                  className={
                    stat.trend === "up" ? "text-green-500" : "text-red-500"
                  }
                >
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Orders */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest orders from your store</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-4">
                    <div className="font-medium">{order.id}</div>
                    <div>
                      <div className="font-medium">{order.customer}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {order.time}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="font-medium">{order.amount}</div>
                    <div
                      className={`px-2 py-1 rounded-full text-xs ${
                        order.status === "Completed"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                      }`}
                    >
                      {order.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Add New Product</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Add products to inventory
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Process Return</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Handle customer returns
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Generate Report</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Create sales reports
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Manage Users</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Add or edit user accounts
              </div>
            </button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardOverview;
