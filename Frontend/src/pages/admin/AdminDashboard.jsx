import React from 'react'
import { Routes, Route } from 'react-router-dom'
import DashboardOverview from './DashboardOverview'
import ProductsPage from './ProductsPage'
import OrdersPage from './OrdersPage'
import CustomersPage from './CustomersPage'
import ReportsPage from './ReportsPage'
import UsersPage from './UsersPage'
import StoresPage from './StoresPage'
import SettingsPage from './SettingsPage'

const AdminDashboard = () => {
  return (
    <Routes>
      <Route index element={<DashboardOverview />} />
      <Route path="products" element={<ProductsPage />} />
      <Route path="orders" element={<OrdersPage />} />
      <Route path="customers" element={<CustomersPage />} />
      <Route path="reports" element={<ReportsPage />} />
      <Route path="users" element={<UsersPage />} />
      <Route path="stores" element={<StoresPage />} />
      <Route path="settings" element={<SettingsPage />} />
    </Routes>
  )
}

export default AdminDashboard
