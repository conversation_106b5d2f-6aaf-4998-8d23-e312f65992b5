import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
  Plus,
  Users,
  Edit,
  Trash2,
  Shield,
  Eye,
  Edit3,
  Trash,
  Package,
  BarChart3,
} from "lucide-react";

const TeamManagementPage = () => {
  const [showAddUser, setShowAddUser] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "cashier",
    permissions: [],
  });

  // Mock team members data
  const [teamMembers] = useState([
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "manager",
      permissions: [
        "pos",
        "inventory",
        "reports",
        "customers",
        "orders_read",
        "orders_update",
      ],
      status: "active",
      lastLogin: "2 hours ago",
    },
    {
      id: "4",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "cashier",
      permissions: ["pos"],
      status: "active",
      lastLogin: "1 day ago",
    },
    {
      id: "6",
      name: "Emma Wilson",
      email: "<EMAIL>",
      role: "cashier",
      permissions: ["pos"],
      status: "inactive",
      lastLogin: "1 week ago",
    },
  ]);

  const roles = [
    {
      value: "manager",
      label: "Manager",
      description: "Can manage most operations",
    },
    { value: "cashier", label: "Cashier", description: "POS access only" },
    {
      value: "inventory_manager",
      label: "Inventory Manager",
      description: "Manage products and inventory",
    },
  ];

  const availablePermissions = [
    {
      id: "pos",
      label: "POS Access",
      description: "Access to point of sale system",
    },
    {
      id: "inventory",
      label: "Inventory Management",
      description: "Manage products and stock",
    },
    {
      id: "orders_read",
      label: "View Orders",
      description: "View order history",
    },
    {
      id: "orders_update",
      label: "Manage Orders",
      description: "Update and process orders",
    },
    {
      id: "customers",
      label: "Customer Management",
      description: "Manage customer data",
    },
    {
      id: "reports",
      label: "Reports",
      description: "Access to reports and analytics",
    },
  ];

  const handleAddUser = () => {
    // Mock add user functionality
    console.log("Adding user:", newUser);
    setShowAddUser(false);
    setNewUser({ name: "", email: "", role: "cashier", permissions: [] });
  };

  const getPermissionIcon = (permission) => {
    switch (permission) {
      case "pos":
        return <Shield className="h-3 w-3" />;
      case "inventory":
        return <Package className="h-3 w-3" />;
      case "orders_read":
        return <Eye className="h-3 w-3" />;
      case "orders_update":
        return <Edit3 className="h-3 w-3" />;
      case "customers":
        return <Users className="h-3 w-3" />;
      case "reports":
        return <BarChart3 className="h-3 w-3" />;
      default:
        return <Shield className="h-3 w-3" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Team Management</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your team members and their permissions
          </p>
        </div>
        <Button onClick={() => setShowAddUser(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Team Member
        </Button>
      </div>

      {/* Add User Form */}
      {showAddUser && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Team Member</CardTitle>
            <CardDescription>
              Create a new user account for your organization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Full Name</label>
                <Input
                  value={newUser.name}
                  onChange={(e) =>
                    setNewUser({ ...newUser, name: e.target.value })
                  }
                  placeholder="Enter full name"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  value={newUser.email}
                  onChange={(e) =>
                    setNewUser({ ...newUser, email: e.target.value })
                  }
                  placeholder="Enter email address"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Role</label>
              <select
                value={newUser.role}
                onChange={(e) =>
                  setNewUser({ ...newUser, role: e.target.value })
                }
                className="w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {roles.map((role) => (
                  <option key={role.value} value={role.value}>
                    {role.label} - {role.description}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Permissions</label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {availablePermissions.map((permission) => (
                  <label
                    key={permission.id}
                    className="flex items-center space-x-2 text-sm"
                  >
                    <input
                      type="checkbox"
                      checked={newUser.permissions.includes(permission.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewUser({
                            ...newUser,
                            permissions: [
                              ...newUser.permissions,
                              permission.id,
                            ],
                          });
                        } else {
                          setNewUser({
                            ...newUser,
                            permissions: newUser.permissions.filter(
                              (p) => p !== permission.id
                            ),
                          });
                        }
                      }}
                      className="rounded"
                    />
                    <span>{permission.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={handleAddUser}>Add User</Button>
              <Button variant="outline" onClick={() => setShowAddUser(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Team Members List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Members
          </CardTitle>
          <CardDescription>
            Manage your team members and their access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {teamMembers.map((member) => (
              <div
                key={member.id}
                className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      {member.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium">{member.name}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {member.email}
                    </div>
                    <div className="text-xs text-gray-400">
                      Last login: {member.lastLogin}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-medium capitalize">
                      {member.role}
                    </div>
                    <div className="flex items-center space-x-1 mt-1">
                      {member.permissions.slice(0, 3).map((permission) => (
                        <span
                          key={permission}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700"
                        >
                          {getPermissionIcon(permission)}
                          <span className="ml-1">{permission}</span>
                        </span>
                      ))}
                      {member.permissions.length > 3 && (
                        <span className="text-xs text-gray-500">
                          +{member.permissions.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <div
                      className={`px-2 py-1 rounded-full text-xs ${
                        member.status === "active"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                      }`}
                    >
                      {member.status}
                    </div>

                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamManagementPage;
