import React from 'react'
import { Routes, Route } from 'react-router-dom'
import SuperAdminOverview from './SuperAdminOverview'
import OrganizationsPage from './OrganizationsPage'
import PlatformUsersPage from './PlatformUsersPage'
import PlatformSettingsPage from './PlatformSettingsPage'
import PlatformAnalyticsPage from './PlatformAnalyticsPage'

const SuperAdminDashboard = () => {
  return (
    <Routes>
      <Route index element={<SuperAdminOverview />} />
      <Route path="organizations" element={<OrganizationsPage />} />
      <Route path="platform-users" element={<PlatformUsersPage />} />
      <Route path="analytics" element={<PlatformAnalyticsPage />} />
      <Route path="settings" element={<PlatformSettingsPage />} />
    </Routes>
  )
}

export default SuperAdminDashboard
