import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { 
  Building2, 
  Users, 
  DollarSign, 
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react'

const SuperAdminOverview = () => {
  // Mock platform-wide data
  const platformStats = [
    {
      title: "Total Organizations",
      value: "247",
      change: "+12.5%",
      trend: "up",
      icon: Building2,
      description: "Active paying customers"
    },
    {
      title: "Platform Revenue",
      value: "$47,892",
      change: "+18.2%",
      trend: "up",
      icon: DollarSign,
      description: "Monthly recurring revenue"
    },
    {
      title: "Total Users",
      value: "3,247",
      change: "+8.1%",
      trend: "up",
      icon: Users,
      description: "Across all organizations"
    },
    {
      title: "System Health",
      value: "99.9%",
      change: "+0.1%",
      trend: "up",
      icon: Activity,
      description: "Platform uptime"
    }
  ]

  const recentOrganizations = [
    { id: 'org_247', name: 'Pizza Palace', plan: 'Pro', users: 12, status: 'Active', joined: '2 days ago' },
    { id: 'org_246', name: 'Coffee Corner', plan: 'Basic', users: 3, status: 'Active', joined: '5 days ago' },
    { id: 'org_245', name: 'Fashion Store', plan: 'Enterprise', users: 25, status: 'Active', joined: '1 week ago' },
    { id: 'org_244', name: 'Book Shop', plan: 'Basic', users: 2, status: 'Trial', joined: '1 week ago' },
    { id: 'org_243', name: 'Electronics Hub', plan: 'Pro', users: 8, status: 'Active', joined: '2 weeks ago' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Platform Overview</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Welcome to Ultra POS Super Admin Dashboard. Monitor your SaaS platform performance.
        </p>
      </div>

      {/* Platform Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {platformStats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                {stat.trend === 'up' ? (
                  <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Organizations */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Organizations</CardTitle>
            <CardDescription>
              Latest organizations that joined the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrganizations.map((org) => (
                <div key={org.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="font-medium">{org.id}</div>
                    <div>
                      <div className="font-medium">{org.name}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {org.users} users • {org.joined}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">{org.plan}</div>
                    <div className={`px-2 py-1 rounded-full text-xs ${
                      org.status === 'Active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                    }`}>
                      {org.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Platform Actions */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Platform Actions</CardTitle>
            <CardDescription>
              Common platform management tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Add New Organization</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Create a new customer account</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Platform Maintenance</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">System updates and maintenance</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Generate Platform Report</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Platform-wide analytics</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <div className="font-medium">Billing Management</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Manage subscriptions and billing</div>
            </button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SuperAdminOverview
