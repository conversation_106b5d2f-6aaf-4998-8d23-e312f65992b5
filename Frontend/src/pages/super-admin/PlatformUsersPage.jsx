import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Users } from 'lucide-react'

const PlatformUsersPage = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Platform Users</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage platform administrators and support staff
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Platform User Management
          </CardTitle>
          <CardDescription>
            This page will contain platform user management functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Users className="h-16 w-16 mx-auto mb-4 text-gray-500 dark:text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Platform User Management Coming Soon</h3>
            <p className="text-gray-500 dark:text-gray-400">
              This section will include platform admin accounts, support staff, and system access management.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PlatformUsersPage
